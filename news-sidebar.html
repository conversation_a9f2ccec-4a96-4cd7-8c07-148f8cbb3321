<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{companyInfo.name}}</title>
    <!-- favicons Icons -->




    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">

    <!-- 个人中心样式 -->
    <style>
        .pagination-container {
            text-align: center;
            margin-top: 40px;
            margin-bottom: 20px;
        }

        .pagination-btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #015fc9;
            color: #015fc9;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
        }

        .pagination-btn:hover,
        .pagination-btn.active {
            background-color: #015fc9;
            color: #fff;
        }

        .pagination-btn:disabled {
            border-color: #ccc;
            color: #ccc;
            background-color: #f9f9f9;
            cursor: not-allowed;
        }

        .pagination-info {
            display: inline-block;
            margin: 0 15px;
            color: #666;
            font-size: 14px;
            vertical-align: middle;
        }

        .record-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            text-decoration: none;
            display: block;
            margin-bottom: 8px;
        }

        .record-title:hover {
            color: #015fc9;
            text-decoration: none;
        }

        .record-meta {
            font-size: 14px;
            color: #666;
        }

        .record-type-badge {
            display: inline-block;
            padding: 2px 8px;
            background-color: #f0f0f0;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }

        .record-type-1 { background-color: #e3f2fd; color: #1976d2; }
        .record-type-2 { background-color: #f3e5f5; color: #7b1fa2; }
        .record-type-3 { background-color: #e8f5e8; color: #388e3c; }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ddd;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        /* 左侧导航栏样式优化 */
        .sidebar__category-list li a {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .sidebar__category-list li a i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        .sidebar__category-list li a .fas.fa-angle-double-right {
            margin-left: auto;
            margin-right: 0;
            width: auto;
        }

        .sidebar__category-list li.active a {
            background-color: #015fc9;
            color: #fff;
            border-radius: 8px;
        }

        .sidebar__category-list li:hover a {
            background-color: #f8f9fa;
            color: #015fc9;
            border-radius: 8px;
        }

        .sidebar__category-list li.active:hover a {
            background-color: #015fc9;
            color: #fff;
        }

        .sidebar__title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 25px;
            color: #333;
            border-bottom: 3px solid #015fc9;
            padding-bottom: 10px;
        }

        /* 响应式调整 */
        @media (max-width: 991px) {
            .sidebar__category-list li a {
                padding: 12px 15px;
                font-size: 15px;
            }

            .sidebar__category-list li a i {
                font-size: 16px;
                margin-right: 10px;
            }
        }
    </style>
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->


    <div id="app">
    <div class="page-wrapper">
        <header class="main-header clearfix">
            <div class="main-header__top">
                <div class="container">
                    <div class="main-header__top-inner">
                        <div class="main-header__top-address">
                            <ul class="list-unstyled main-header__top-address-list">
                                <li>
                                    <i class="icon">
                                        <span class="icon-pin"></span>
                                    </i>
                                    <div class="text">
                                        <p>{{companyInfo.address}}</p>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon">
                                        <span class="icon-email"></span>
                                    </i>
                                    <div class="text">
                                        <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="main-menu clearfix">
                <div class="main-menu__wrapper clearfix">
                    <div class="container">
                        <div class="main-menu__wrapper-inner clearfix">
                            <div class="main-menu__left">
                                <div class="main-menu__logo">
                                    <a><img :src="companyInfo.logoImg" alt="" style="height: 27px"></a>
                                </div>
                                <div class="main-menu__main-menu-box">
                                    <div class="main-menu__main-menu-box-inner">
                                        <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i class="fa fa-bars"></i></a>
                                        <ul class="main-menu__list one-page-scroll-menu">
                                            <li class=" megamenu scrollToLink">
                                                <a href="index-one-page.html">首页 </a>
                                            </li>
                                            <li class="scrollToLink">
                                                <a href="company.html">保险公司 </a>
                                            </li>
                                            <li class="scrollToLink">
                                                <a href="products.html">保险产品</a>
                                            </li>
                                            <li class="scrollToLink">
                                                <a href="news.html">新闻资讯</a>
                                            </li>
                                            <li class="scrollToLink">
                                                <a href="about.html">关于我们</a>
                                            </li>
                                            <li class="scrollToLink current" style="margin-right: 37px;">
                                                <a href="news-sidebar.html">个人中心</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <div class="stricky-header stricked-menu main-menu">
            <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
        </div><!-- /.stricky-header -->

        <!--Page Header Start-->
        <section class="page-header">
            <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
            </div>
            <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
            <div class="container">
                <div class="page-header__inner">
                    <ul class="thm-breadcrumb list-unstyled">
                        <li><a href="index.html">首页</a></li>
                        <li><span>/</span></li>
                        <li>个人中心</li>
                    </ul>
                    <h2>个人中心</h2>
                </div>
            </div>
        </section>
        <!--Page Header End-->

        <!--News Sidebar Start-->
        <section class="news-sidebar">
            <div class="container">
                <div class="row">
                    <!-- 左侧导航栏 -->
                    <div class="col-xl-4 col-lg-5">
                        <div class="sidebar">
                            <!-- 导航菜单 -->
                            <div class="sidebar__single sidebar__category">
                                <h3 class="sidebar__title">个人中心</h3>
                                <ul class="sidebar__category-list list-unstyled">
                                    <li :class="{active: currentRecordType === 1}">
                                        <a href="#" @click.prevent="switchRecordType(1)">
                                            <i class="fas fa-history"></i> 历史浏览记录 <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li :class="{active: currentRecordType === 2}">
                                        <a href="#" @click.prevent="switchRecordType(2)">
                                            <i class="fas fa-heart"></i> 收藏记录 <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li :class="{active: currentRecordType === 3}">
                                        <a href="#" @click.prevent="switchRecordType(3)">
                                            <i class="fas fa-thumbs-up"></i> 点赞记录 <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                    <li :class="{active: currentRecordType === 4}">
                                        <a href="#" @click.prevent="switchRecordType(4)">
                                            <i class="fas fa-user"></i> 个人资料 <span class="fas fa-angle-double-right"></span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧内容区域 -->
                    <div class="col-xl-8 col-lg-7">
                        <div class="news-sideabr__left">
                            <!-- 搜索和筛选区域 -->
                            <div class="sidebar__single sidebar__search" style="margin-bottom: 30px;">
                                <div class="sidebar__search-form">
                                    <input v-model="searchKeyword" type="search" placeholder="搜索记录标题" @keyup.enter="handleSearch">
                                    <button @click="handleSearch" type="submit"><i class="icon-magnifying-glass"></i></button>
                                </div>
                            </div>

                            <!-- 筛选器 -->
                            <div class="sidebar__single" style="margin-bottom: 30px;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <select v-model="filterContentType" @change="searchRecords" class="selectpicker form-control" data-style="btn-outline-primary">
                                            <option value="">全部类型</option>
                                            <option value="1">保险产品</option>
                                            <option value="2">保险资讯</option>
                                            <option value="3">保险公司</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <input v-model="filterStartTime" @change="searchRecords" type="date" class="form-control" placeholder="开始时间">
                                    </div>
                                </div>
                            </div>

                            <!-- 记录列表 -->
                            <div class="news-sideabr__content">
                                <div v-if="loading" class="loading-state">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <p>加载中...</p>
                                </div>

                                <div v-else-if="recordList.length === 0" class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>暂无记录</p>
                                </div>

                                <div v-else>
                                    <div v-for="record in recordList" :key="record.id" class="record-item">
                                        <a :href="getRecordUrl(record)" class="record-title" target="_blank">
                                            {{ record.title }}
                                        </a>
                                        <div class="record-meta">
                                            <span>{{ formatDate(record.createTime) }}</span>
                                            <span :class="'record-type-badge record-type-' + record.contentType">
                                                {{ getContentTypeName(record.contentType) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination-container" v-if="totalPages > 1">
                                <button class="pagination-btn" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                                    上一页
                                </button>
                                <span class="pagination-info">第 {{ currentPage }} / {{ totalPages }} 页，共 {{ totalRecords }} 条记录</span>
                                <button class="pagination-btn" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--News Sidebar End-->

        <!--Site Footer Start-->
        <footer class="site-footer">
            <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
            </div>
            <div class="container">
                <div class="site-footer__top">
                    <div class="row">
                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                            <div class="footer-widget__column footer-widget__contact clearfix">
                                <h3 class="footer-widget__title">公司地址</h3>
                                <ul class="footer-widget__contact-list list-unstyled clearfix">
                                    <li>
                                        <div class="icon">
                                            <span class="icon-pin"></span>
                                        </div>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                </ul>
                                <div class="footer-widget__open-hour">
                                    <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                    <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                            <div class="footer-widget__column footer-widget__newsletter">
                                <h3 class="footer-widget__title">联系电话</h3>
                                <div class="footer-widget__phone">
                                    <div class="footer-widget__phone-icon">
                                        <span class="icon-telephone"></span>
                                    </div>
                                    <div class="footer-widget__phone-text">
                                        <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                        <p>欢迎拨打</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="site-footer__bottom">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="site-footer__bottom-inner">
                                <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                        href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!--Site Footer End-->

    </div><!-- /.page-wrapper -->

    <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>
        <!-- /.mobile-nav__overlay -->
        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i class="fa fa-times"></i></span>

            <div class="logo-box">
                <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                        alt=""></a>
            </div>
            <!-- /.logo-box -->
            <div class="mobile-nav__container"></div>
            <!-- /.mobile-nav__container -->

        </div>
        <!-- /.mobile-nav__content -->
    </div>
    </div><!-- /#app -->
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script>
        const app = new Vue({
            el: '#app',
            data: {
                expanded: false,
                // 公司信息
                companyInfo: {
                    name: '',
                    address: '',
                    mailbox: '',
                    logoImg: '',
                    phone: '',
                    copyright: '',
                    icpNumber: ''
                },
                // 当前记录类型：1.历史浏览 2.收藏 3.点赞 4.个人资料
                currentRecordType: 1,
                // 记录列表
                recordList: [],
                // 分页信息
                currentPage: 1,
                pageSize: 10,
                totalPages: 0,
                totalRecords: 0,
                // 搜索和筛选
                searchKeyword: '',
                filterContentType: '',
                filterStartTime: '',
                // 状态
                loading: false
            },
            mounted() {
                this.getCompanyInfo();
                this.loadRecords();
                // 初始化bootstrap-select
                this.$nextTick(() => {
                    $('.selectpicker').selectpicker();
                });
            },
            methods: {
                // 获取认证头
                getAuthHeaders() {
                    const token = localStorage.getItem('X-Access-Token');
                    return {
                        'Content-Type': 'application/json',
                        'X-Access-Token': token || ''
                    };
                },

                // 获取公司信息
                async getCompanyInfo() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                            method: 'get',
                            headers: this.getAuthHeaders()
                        });
                        const data = await response.json();
                        if (data && data.success && data.result) {
                            this.companyInfo = data.result;
                            document.title = this.companyInfo.name;
                        }
                    } catch (error) {
                        console.error('获取公司信息失败:', error);
                    }
                },

                // 切换记录类型
                switchRecordType(recordType) {
                    if (recordType === 4) {
                        alert('个人资料功能开发中...');
                        return;
                    }

                    this.currentRecordType = recordType;
                    this.currentPage = 1;
                    this.searchKeyword = '';
                    this.filterContentType = '';
                    this.filterStartTime = '';
                    this.loadRecords();

                    // 更新selectpicker
                    this.$nextTick(() => {
                        $('.selectpicker').selectpicker('refresh');
                    });
                },

                // 加载记录列表
                async loadRecords() {
                    this.loading = true;
                    try {
                        const queryData = {
                            recordType: this.currentRecordType,
                            title: this.searchKeyword || undefined,
                            contentType: this.filterContentType || undefined,
                            startTime: this.filterStartTime ? this.filterStartTime + ' 00:00:00' : undefined,
                            endTime: this.filterStartTime ? this.filterStartTime + ' 23:59:59' : undefined
                        };

                        // 移除空值
                        Object.keys(queryData).forEach(key => {
                            if (queryData[key] === undefined) {
                                delete queryData[key];
                            }
                        });

                        const response = await fetch(
                            `${jeeApi}/jeecg-boot/wh/record/getRecordListWithTitle?pageNo=${this.currentPage}&pageSize=${this.pageSize}`,
                            {
                                method: 'POST',
                                headers: this.getAuthHeaders(),
                                body: JSON.stringify(queryData)
                            }
                        );

                        const data = await response.json();

                        if (data.success) {
                            this.recordList = data.result.records || [];
                            this.totalRecords = data.result.total || 0;
                            this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
                        } else {
                            console.error('获取记录失败:', data.message);
                            this.recordList = [];
                            this.totalRecords = 0;
                            this.totalPages = 0;
                        }
                    } catch (error) {
                        console.error('请求失败:', error);
                        this.recordList = [];
                        this.totalRecords = 0;
                        this.totalPages = 0;
                    } finally {
                        this.loading = false;
                    }
                },

                // 处理搜索按钮点击
                handleSearch() {
                    this.searchRecords();
                },

                // 搜索记录
                searchRecords() {
                    this.currentPage = 1;
                    this.loadRecords();
                },

                // 切换页码
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        this.currentPage = page;
                        this.loadRecords();
                    }
                },

                // 格式化日期
                formatDate(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },

                // 获取内容类型名称
                getContentTypeName(contentType) {
                    const typeMap = {
                        1: '保险产品',
                        2: '保险资讯',
                        3: '保险公司'
                    };
                    return typeMap[contentType] || '未知';
                },

                // 获取记录跳转链接
                getRecordUrl(record) {
                    // 根据内容类型和ID生成对应的链接
                    const baseUrl = window.location.origin;
                    switch (record.contentType) {
                        case 1: // 保险产品
                            return `${baseUrl}/product-details.html?id=${record.contentId}`;
                        case 2: // 保险资讯
                            return `${baseUrl}/news-details.html?id=${record.contentId}`;
                        case 3: // 保险公司
                            return `${baseUrl}/company-details.html?id=${record.contentId}`;
                        default:
                            return '#';
                    }
                }
            }
        });
    </script>
</body>

</html>